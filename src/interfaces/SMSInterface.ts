import { MessageInterface, ChatResponse, SessionCreateRequest } from '../models/types';
import { ChatService } from '../core/ChatService';
import { ForaChat } from '../operations';
import { DBOS } from '@dbos-inc/dbos-sdk';
import { logger } from '../utils/Logger';

// Future SMS interface using Twilio or similar service
export class SMSInterface implements MessageInterface {
  private chatService: ChatService;
  private twilioClient?: any; // Would be Twilio client
  private phoneNumber: string;
  private sessions: Map<string, any> = new Map(); // Cache sessions by phone number

  constructor(chatService: ChatService, phoneNumber: string) {
    this.chatService = chatService;
    this.phoneNumber = phoneNumber;
    // this.twilioClient = twilio(accountSid, authToken); // Future implementation
  }

  private async getOrCreateSession(phoneNumber: string): Promise<any> {
    // Check cache first
    if (this.sessions.has(phoneNumber)) {
      const session = this.sessions.get(phoneNumber);
      // Update session activity
      try {
        await ForaChat.updateSessionActivity(session.id);
        return session;
      } catch (error) {
        // Session might be expired, remove from cache
        this.sessions.delete(phoneNumber);
      }
    }

    // Try to get existing session from database
    try {
      const userIdentifier = `sms_${phoneNumber}`;
      const handle = await DBOS.startWorkflow(ForaChat).getSessionByUserAndChannel(userIdentifier, 'sms');
      let session = await handle.getResult();

      if (!session) {
        // Create new session
        const sessionRequest: SessionCreateRequest = {
          userIdentifier,
          channel: 'sms',
          metadata: {
            phoneNumber,
            provider: 'twilio' // or whatever SMS provider is used
          }
        };

        const createHandle = await DBOS.startWorkflow(ForaChat).createSession(sessionRequest);
        session = await createHandle.getResult();
        logger.info(`Created new SMS session for ${phoneNumber}: ${session.id}`);
      } else {
        // Update existing session activity
        await ForaChat.updateSessionActivity(session.id);
        logger.info(`Restored SMS session for ${phoneNumber}: ${session.id}`);
      }

      // Cache the session
      this.sessions.set(phoneNumber, session);
      return session;
    } catch (error) {
      logger.error(`Error managing SMS session for ${phoneNumber}`, error);
      throw error;
    }
  }

  async sendMessage(message: string): Promise<void> {
    // Future implementation with Twilio
    console.log(`SMS would send to ${this.phoneNumber}: ${message}`);
    
    /*
    await this.twilioClient.messages.create({
      body: message,
      from: process.env.TWILIO_PHONE_NUMBER,
      to: this.phoneNumber
    });
    */
  }

  async receiveMessage(): Promise<string> {
    // This would be handled by webhook in a real implementation
    throw new Error('SMS receiveMessage should be handled by webhook');
  }

  formatResponse(response: ChatResponse): string {
    // Format for SMS - shorter, more concise
    let formatted = `💼 ${response.theme}\n\n`;
    
    // Limit to first 2-3 messages for SMS length constraints
    const messagesToShow = response.reply.slice(0, 3);
    
    messagesToShow.forEach((message, index) => {
      // Use emojis to represent characters
      const characterEmoji = this.getCharacterEmoji(message.character);
      formatted += `${characterEmoji} ${message.text}\n\n`;
    });
    
    if (response.skills && response.skills.length > 0) {
      formatted += `🎯 Skills: ${response.skills.slice(0, 3).join(', ')}`;
    }
    
    // Ensure message isn't too long for SMS (160 char limit for single SMS)
    if (formatted.length > 1500) { // Leave room for multiple SMS segments
      formatted = formatted.substring(0, 1450) + '...';
    }
    
    return formatted;
  }

  private getCharacterEmoji(character: string): string {
    switch (character.toLowerCase()) {
      case 'fora':
        return '🌟'; // Enthusiastic leader
      case 'jan':
        return '📊'; // Data-driven
      case 'lou':
        return '🤝'; // People-focused
      default:
        return '💬';
    }
  }

  // Webhook handler for incoming SMS messages
  async handleIncomingMessage(from: string, body: string): Promise<void> {
    try {
      // Get or create session for this phone number
      const session = await this.getOrCreateSession(from);

      // Log user request
      logger.info(`[SMS] User request: "${body}" | Phone: ${from} | Session: ${session.id} | Conversation: ${session.conversation_id || 'new'}`);

      let result;
      if (session.conversation_id) {
        // Continue existing conversation
        const handle = await DBOS.startWorkflow(ForaChat).chatWorkflow(body, session.conversation_id);
        result = await handle.getResult();
      } else {
        // Start new conversation
        const handle = await DBOS.startWorkflow(ForaChat).chatWorkflow(body);
        result = await handle.getResult();

        // Update session with new conversation ID
        if (result.conversationId) {
          await ForaChat.updateSessionConversation(session.id, result.conversationId);
          session.conversation_id = result.conversationId;
          this.sessions.set(from, session); // Update cache
        }
      }

      const formattedResponse = this.formatResponse(result);
      await this.sendMessage(formattedResponse);

    } catch (error) {
      logger.error(`Error processing SMS from ${from}`, error);
      await this.sendMessage('Sorry, I had trouble processing your message. Please try again.');
    }
  }

  // Method to get session info for a phone number (useful for debugging/admin)
  async getSessionInfo(phoneNumber: string): Promise<any> {
    try {
      const userIdentifier = `sms_${phoneNumber}`;
      const handle = await DBOS.startWorkflow(ForaChat).getSessionByUserAndChannel(userIdentifier, 'sms');
      return await handle.getResult();
    } catch (error) {
      logger.error(`Error getting SMS session info for ${phoneNumber}`, error);
      return null;
    }
  }

  // Method to clear session cache (useful for testing or admin operations)
  clearSessionCache(phoneNumber?: string): void {
    if (phoneNumber) {
      this.sessions.delete(phoneNumber);
      logger.info(`Cleared SMS session cache for ${phoneNumber}`);
    } else {
      this.sessions.clear();
      logger.info('Cleared all SMS session cache');
    }
  }

  // Setup webhook endpoint (would be used in Express app)
  setupWebhook(app: any): void {
    app.post('/sms/webhook', async (req: any, res: any) => {
      const { From, Body } = req.body;
      
      try {
        await this.handleIncomingMessage(From, Body);
        res.status(200).send('OK');
      } catch (error) {
        console.error('SMS webhook error:', error);
        res.status(500).send('Error processing message');
      }
    });
  }

  // Utility methods for SMS management
  async sendWelcomeMessage(): Promise<void> {
    const welcomeMessage = `👋 Welcome to ForaChat! 

I'm here to help with workplace skills. Ask me about:
• Communication 💬
• Leadership 👑  
• Teamwork 🤝
• Conflict resolution ⚖️
• Feedback 📝

Just text me your question!`;

    await this.sendMessage(welcomeMessage);
  }

  async sendHelpMessage(): Promise<void> {
    const helpMessage = `📚 ForaChat Help:

Ask questions like:
• "How do I give better feedback?"
• "I'm having team conflict"
• "Leadership tips for new managers"

Get advice from Fora 🌟, Jan 📊, and Lou 🤝!`;

    await this.sendMessage(helpMessage);
  }
}
