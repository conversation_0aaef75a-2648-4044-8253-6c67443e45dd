import { DBOS } from '@dbos-inc/dbos-sdk';
import { DelayedMessage } from '../models/types';
import { ChatService } from '../core/ChatService';
import { ConversationService } from '../core/ConversationService';
import { SessionManager } from './SessionManager';
import { MessageStreamer } from './MessageStreamer';
import { EngagementMonitor } from './EngagementMonitor';
import { MessageHandler } from './MessageHandler';
import { WorkflowManager } from './WorkflowManager';
import { MessageQueueProcessor } from './MessageQueueProcessor';
import { ExtendedWorkflowStatus } from './types';
import { logger } from '../utils/Logger';
import WebSocket from 'ws';

export class StreamingChatService {
  private sessionManager: SessionManager;
  private messageStreamer: MessageStreamer;
  private engagementMonitor: EngagementMonitor;
  private messageHandler: MessageHandler;
  private workflowManager: WorkflowManager;
  private messageQueueProcessor: MessageQueueProcessor;

  constructor(private chatService: ChatService) {
    // Initialize components in dependency order
    this.sessionManager = new SessionManager();
    this.messageStreamer = new MessageStreamer(this.sessionManager);
    this.engagementMonitor = new EngagementMonitor(this.sessionManager);
    this.messageQueueProcessor = new MessageQueueProcessor(
      this.sessionManager,
      this.messageStreamer,
      this.chatService
    );
    this.messageHandler = new MessageHandler(
      this.sessionManager,
      this.messageStreamer,
      this.engagementMonitor,
      this.chatService,
      this.messageQueueProcessor // Pass the MessageQueueProcessor
    );
    this.workflowManager = new WorkflowManager(
      this.sessionManager,
      this.messageStreamer,
      this.engagementMonitor
    );
  }

  createSession(sessionId: string, ws: WebSocket, dbSession?: any): void {
    this.sessionManager.createSession(sessionId, ws, dbSession);

    ws.on('message', async (data) => {
      try {
        const message = JSON.parse(data.toString());
        await this.handleMessage(sessionId, message);
      } catch (error) {
        this.messageStreamer.sendError(sessionId, 'Invalid message format');
      }
    });

    ws.on('close', () => {
      this.cleanupSession(sessionId);
    });

    // Send welcome message
    this.messageStreamer.sendMessage(sessionId, {
      type: 'connected',
      sessionId
    });
  }

  private async handleMessage(sessionId: string, message: any): Promise<void> {
    switch (message.type) {
      case 'chat':
      case 'interrupt':
        await this.messageHandler.handleMessage(sessionId, message);
        break;
      case 'start_extended_workflow':
        await this.workflowManager.triggerExtendedWorkflow(sessionId);
        break;
      case 'get_extended_workflow_status':
        const status = this.workflowManager.getExtendedWorkflowStatus(sessionId);
        this.messageStreamer.sendMessage(sessionId, {
          type: 'extended_workflow_status',
          ...status
        });
        break;
      default:
        this.messageStreamer.sendError(sessionId, 'Unknown message type');
    }
  }

  private cleanupSession(sessionId: string): void {
    this.sessionManager.cleanupSession(sessionId);
  }

  getSessionCount(): number {
    return this.sessionManager.getSessionCount();
  }

  // Delegate to MessageQueueProcessor
  async pollForDelayedThoughts(sessionId: string): Promise<void> {
    await this.messageQueueProcessor.pollForDelayedThoughts(sessionId);
  }

  // Delegate to MessageStreamer for backward compatibility
  async streamDelayedMessages(sessionId: string, messages: DelayedMessage[], skills?: string[]): Promise<void> {
    await this.messageStreamer.streamDelayedMessages(sessionId, messages, skills);

    // After streaming, start polling and extended workflow
    const session = this.sessionManager.getSession(sessionId);
    if (session && session.conversationId) {
      // Get the current highest message ID to avoid re-polling initial messages
      const allMessages = await ConversationService.getDelayedThoughts(session.conversationId);
      if (allMessages.length > 0) {
        session.lastMessageId = Math.max(...allMessages.map(m => m.id || 0));
        logger.info(`Set lastMessageId to ${session.lastMessageId} to avoid re-polling initial messages`);
      }

      // Start polling for delayed character thoughts
      await this.pollForDelayedThoughts(sessionId);

      // Start extended workflow
      await this.workflowManager.startExtendedWorkflow(sessionId);
    }
  }

  // Method to start polling without streaming (for cases where no immediate messages)
  async startPollingForSession(sessionId: string): Promise<void> {
    const session = this.sessionManager.getSession(sessionId);
    if (session && session.conversationId) {
      // Get the current highest message ID to avoid re-polling initial messages
      const allMessages = await ConversationService.getDelayedThoughts(session.conversationId);
      if (allMessages.length > 0) {
        session.lastMessageId = Math.max(...allMessages.map(m => m.id || 0));
        logger.info(`Set lastMessageId to ${session.lastMessageId} to avoid re-polling initial messages`);
      }

      // Start polling for delayed character thoughts
      await this.pollForDelayedThoughts(sessionId);

      // Start extended workflow
      await this.workflowManager.startExtendedWorkflow(sessionId);
    }
  }

  // Legacy methods for backward compatibility - delegate to components
  async startChat(sessionId: string, userMessage: string, conversationId?: number): Promise<void> {
    const session = this.sessionManager.getSession(sessionId);
    if (!session) return;

    // Set the conversation ID if provided
    if (conversationId) {
      session.conversationId = conversationId;
    }

    try {
      // Use the ChatService to process the message
      const result = await this.chatService.processUserMessage({ text: userMessage });
      const response = result.response;

      if (!response) {
        this.messageStreamer.sendError(sessionId, 'No response from chat service');
        return;
      }

      // Set the conversation ID from the result
      session.conversationId = result.conversationId;
      await this.pollForDelayedThoughts(sessionId);

      if (response.reply && Array.isArray(response.reply)) {
        this.messageStreamer.sendMessage(sessionId, {
          type: 'chat_start',
          theme: response.theme || 'Chat Response'
        });

        await this.streamDelayedMessages(sessionId, response.reply, response.skills);
      } else {
        this.messageStreamer.sendError(sessionId, 'Invalid response format from LLM');
      }
    } catch (error) {
      this.messageStreamer.sendError(sessionId, 'Failed to process chat message', (error as Error).message);
    }
  }

  // Update the closeSession method to clean up polling interval
  closeSession(sessionId: string): void {
    this.sessionManager.cleanupSession(sessionId);
  }

  // Delegate workflow methods to WorkflowManager
  async triggerExtendedWorkflow(sessionId: string): Promise<boolean> {
    return await this.workflowManager.triggerExtendedWorkflow(sessionId);
  }

  getExtendedWorkflowStatus(sessionId: string): ExtendedWorkflowStatus {
    return this.workflowManager.getExtendedWorkflowStatus(sessionId);
  }

  // Handle workflow timeout extension when user is active
  extendWorkflowTimeout(sessionId: string): void {
    const session = this.sessionManager.getSession(sessionId);
    if (!session) return;

    // Update user activity tracking
    this.engagementMonitor.updateUserActivity(sessionId);

    // Extend the workflow timeout if extended workflow is active
    this.workflowManager.extendWorkflowTimeout(sessionId);
  }
}
