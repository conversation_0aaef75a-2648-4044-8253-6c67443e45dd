import { DBOS } from '@dbos-inc/dbos-sdk';
import { ConversationService } from './ConversationService';
import { ThemeAnalysisService, ThemeAnalysisResult } from './ThemeAnalysisService';
import { CharacterDecisionService } from './CharacterDecisionService';
import { MoodService } from './MoodService';
import { logger } from '../utils/Logger';

export interface NewChatRequest {
  text: string;
  conversationId?: number;
}

export interface NewChatResponse {
  conversationId: number;
  theme: string;
  skills: string[];
  characterDecisions: Array<{
    character: string,
    queued: boolean,
    reasoning: string,
    response?: {text: string, delay: number}
  }>;
  isGeneralGreeting?: boolean;
  isGeneralInquiry?: boolean;
}

/**
 * New chat service implementing character-driven autonomous decision-making
 * This replaces the old script-based system_agent approach
 */
export class NewChatService {
  
  /**
   * Process user message using the new character-driven flow
   */
  @DBOS.workflow()
  static async processUserMessage(request: NewChatRequest): Promise<NewChatResponse> {
    const { text: userMessage, conversationId } = request;
    
    // Step 1: Create or get conversation
    let conversation;
    if (conversationId) {
      conversation = { id: conversationId };
    } else {
      conversation = await ConversationService.createConversation();
      
      // Set random moods for new conversations
      const moods = MoodService.generateRandomMoods();
      await ConversationService.updateCharacterMoods(conversation.id, moods);
    }
    
    // Add user message to conversation
    await ConversationService.addMessage("user", userMessage, conversation.id);
    
    // Step 2: Analyze conversation theme and skills
    const themeAnalysis = await ThemeAnalysisService.analyzeConversationTheme(
      userMessage,
      conversationId
    );
    
    logger.info(`=== NEW CHAT FLOW ===`);
    logger.info(`Conversation: ${conversation.id}`);
    logger.info(`Theme Analysis: ${JSON.stringify(themeAnalysis)}`);
    
    // Step 3: Update conversation metadata
    await ConversationService.updateConversationMetadata(
      conversation.id,
      themeAnalysis.theme,
      themeAnalysis.skills
    );
    
    // Step 4: Handle special cases (greetings, inquiries)
    if (themeAnalysis.isGeneralGreeting) {
      const greetingResult = await NewChatService.handleGeneralGreeting(userMessage, conversation.id);
      return {
        conversationId: conversation.id,
        theme: themeAnalysis.theme,
        skills: themeAnalysis.skills,
        characterDecisions: greetingResult.characterDecisions,
        isGeneralGreeting: true
      };
    }
    
    if (themeAnalysis.isGeneralInquiry) {
      const inquiryResult = await NewChatService.handleGeneralInquiry(userMessage, conversation.id);
      return {
        conversationId: conversation.id,
        theme: themeAnalysis.theme,
        skills: themeAnalysis.skills,
        characterDecisions: inquiryResult.characterDecisions,
        isGeneralInquiry: true
      };
    }
    
    // Step 5: Process character decisions and queue responses
    const characterDecisions = await CharacterDecisionService.processAllCharacterDecisions(
      userMessage,
      conversation.id,
      themeAnalysis
    );
    
    return {
      conversationId: conversation.id,
      theme: themeAnalysis.theme,
      skills: themeAnalysis.skills,
      characterDecisions
    };
  }
  
  /**
   * Handle interrupted conversations with re-evaluation
   */
  @DBOS.workflow()
  static async processInterruptedMessage(
    userMessage: string,
    previousMessages: Array<{character: string, text: string}>,
    conversationId: number
  ): Promise<NewChatResponse> {
    
    // Add user message to conversation
    await ConversationService.addMessage("user", userMessage, conversationId);
    
    // Re-evaluate theme and skills due to interruption
    const themeAnalysis = await ThemeAnalysisService.reEvaluateThemeOnInterruption(
      userMessage,
      conversationId,
      previousMessages
    );
    
    logger.info(`=== INTERRUPTED CHAT FLOW ===`);
    logger.info(`Conversation: ${conversationId}`);
    logger.info(`Re-evaluated Theme Analysis: ${JSON.stringify(themeAnalysis)}`);
    
    // Handle special cases
    if (themeAnalysis.isGeneralGreeting) {
      const greetingResult = await NewChatService.handleGeneralGreeting(userMessage, conversationId);
      return {
        conversationId,
        theme: themeAnalysis.theme,
        skills: themeAnalysis.skills,
        characterDecisions: greetingResult.characterDecisions,
        isGeneralGreeting: true
      };
    }
    
    if (themeAnalysis.isGeneralInquiry) {
      const inquiryResult = await NewChatService.handleGeneralInquiry(userMessage, conversationId);
      return {
        conversationId,
        theme: themeAnalysis.theme,
        skills: themeAnalysis.skills,
        characterDecisions: inquiryResult.characterDecisions,
        isGeneralInquiry: true
      };
    }
    
    // Process character decisions with interruption context
    const characterDecisions = await CharacterDecisionService.processAllCharacterDecisions(
      userMessage,
      conversationId,
      themeAnalysis
    );
    
    return {
      conversationId,
      theme: themeAnalysis.theme,
      skills: themeAnalysis.skills,
      characterDecisions
    };
  }
  
  /**
   * Handle general greetings by selecting one character to respond immediately
   */
  private static async handleGeneralGreeting(
    userMessage: string,
    conversationId: number
  ): Promise<{characterDecisions: Array<{character: string, queued: boolean, reasoning: string}>}> {
    
    // Pick one character at random for immediate greeting response
    const characters = ['Fora', 'Jan', 'Lou'];
    const selectedCharacter = characters[Math.floor(Math.random() * characters.length)];
    
    logger.info(`General greeting detected - randomly selected ${selectedCharacter} to respond immediately`);
    
    // Generate immediate greeting response from selected character
    const decision = await CharacterDecisionService.getCharacterDecision({
      character: selectedCharacter,
      userMessage,
      conversationId,
      themeAnalysis: {
        theme: 'general greeting',
        skills: [],
        isGeneralGreeting: true,
        isGeneralInquiry: false,
        shouldEngageCharacters: true
      },
      isFollowUp: false
    });
    
    const characterDecisions = characters.map(character => ({
      character,
      queued: character === selectedCharacter && decision.shouldRespond,
      reasoning: character === selectedCharacter
        ? (decision.reasoning || 'Selected for greeting response')
        : 'Not selected for greeting response',
      response: character === selectedCharacter && decision.shouldRespond && decision.response
        ? decision.response
        : undefined
    }));
    
    return { characterDecisions };
  }

  /**
   * Handle general inquiries by selecting one character to respond with a cordial reply
   */
  private static async handleGeneralInquiry(
    userMessage: string,
    conversationId: number
  ): Promise<{characterDecisions: Array<{character: string, queued: boolean, reasoning: string}>}> {

    // Pick one character at random for cordial inquiry response
    const characters = ['Fora', 'Jan', 'Lou'];
    const selectedCharacter = characters[Math.floor(Math.random() * characters.length)];

    logger.info(`General inquiry detected - randomly selected ${selectedCharacter} to respond with cordial reply`);

    // Generate cordial response from selected character
    const decision = await CharacterDecisionService.getCharacterDecision({
      character: selectedCharacter,
      userMessage,
      conversationId,
      themeAnalysis: {
        theme: 'general inquiry',
        skills: [],
        isGeneralGreeting: false,
        isGeneralInquiry: true,
        shouldEngageCharacters: true
      },
      isFollowUp: false
    });

    const characterDecisions = characters.map(character => ({
      character,
      queued: character === selectedCharacter && decision.shouldRespond,
      reasoning: character === selectedCharacter
        ? (decision.reasoning || 'Selected for inquiry response')
        : 'Not selected for inquiry response',
      response: character === selectedCharacter && decision.shouldRespond && decision.response
        ? decision.response
        : undefined
    }));

    return { characterDecisions };
  }
}
