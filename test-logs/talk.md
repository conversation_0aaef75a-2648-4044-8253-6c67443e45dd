
# Integration Test Report
Generated: 2025-07-06T15:20:14.288Z
Session ID: 20b37946-190d-4d5e-a7c0-82b514211ec0

## Summary
- Total Tests: 6
- Successful: 6
- Failed: 0
- Success Rate: 100.0%
- Total Duration: 32.86s
- Average Duration: 5.48s

## Test Results

### prompt_1 ✅
- **Prompt**: "the breakroom is my personal hell"
- **Duration**: 2.70s
- **Timestamp**: 2025-07-06T15:19:31.422Z
- **Conversation ID**: 206
- **Message Count**: 3
- **Theme**: Navigating breakroom dynamics
- **Skills**: workplace behavior, communication, relationship building

#### Character Reply Analysis
- **Total Replies**: 3
- **Immediate Replies**: 3
- **Delayed Replies**: 0
- **Average Delay**: 3500ms
- **Character Breakdown**: For<PERSON> (1), <PERSON> (1), <PERSON> (1)

#### Character Replies
1. **Fora** (immediate, 2000ms delay): "Oh no, that sounds rough! 😩 What makes it feel like that for you?"
2. **Lou** (immediate, 3500ms delay): "Yeah, sometimes breakrooms can have weird vibes. Is it the noise, or people's energy?"
3. **Jan** (immediate, 5000ms delay): "Hmm, could be about shared space etiquette. Are there specific behaviors that are bugging you?"

### prompt_2 ✅
- **Prompt**: "it's so awkward"
- **Duration**: 9.59s
- **Timestamp**: 2025-07-06T15:19:36.123Z
- **Conversation ID**: 206
- **Message Count**: 3
- **Theme**: Navigating breakroom dynamics
- **Skills**: workplace behavior, communication, relationship building

#### Character Reply Analysis
- **Total Replies**: 3
- **Immediate Replies**: 3
- **Delayed Replies**: 0
- **Average Delay**: 4000ms
- **Character Breakdown**: Lou (1), Jan (1), Fora (1)

#### Character Replies
1. **Lou** (immediate, 5000ms delay): "Oof, awkward is def a mood killer. Is it like, too quiet or forced convo kinda awkward?"
2. **Jan** (immediate, 3000ms delay): "Lou brings up a good distinction. Is it the lack of interaction that creates it, or the quality of t..."
3. **Fora** (immediate, 4000ms delay): "Honestly, sometimes I just wanna grab my coffee and peace out when it's like that. 😴 No energy for ..."

### prompt_3 ✅
- **Prompt**: "like what do i even say to these people?"
- **Duration**: 5.18s
- **Timestamp**: 2025-07-06T15:19:47.711Z
- **Conversation ID**: 206
- **Message Count**: 3
- **Theme**: Navigating breakroom dynamics
- **Skills**: workplace behavior, communication, relationship building, conversation

#### Character Reply Analysis
- **Total Replies**: 3
- **Immediate Replies**: 3
- **Delayed Replies**: 0
- **Average Delay**: 4667ms
- **Character Breakdown**: Jan (1), Lou (1), Fora (1)

#### Character Replies
1. **Jan** (immediate, 5500ms delay): "For real, sometimes just a simple 'How's your week going?' or 'Anything fun planned for the weekend?..."
2. **Lou** (immediate, 4000ms delay): "Yeah, Jan's right! Or even just commenting on something neutral, like the weather or if there's a ne..."
3. **Fora** (immediate, 4500ms delay): "Ugh, the small talk struggle is real. 😩 Sometimes I just stick to 'Morning!' and make a beeline for..."

### prompt_4 ✅
- **Prompt**: "we have nothing in common they're all like... married with kids"
- **Duration**: 5.49s
- **Timestamp**: 2025-07-06T15:19:54.892Z
- **Conversation ID**: 206
- **Message Count**: 3
- **Theme**: Navigating breakroom dynamics
- **Skills**: relationship building, communication, workplace behavior, conversation, emotional intelligence

#### Character Reply Analysis
- **Total Replies**: 3
- **Immediate Replies**: 3
- **Delayed Replies**: 0
- **Average Delay**: 3500ms
- **Character Breakdown**: Fora (1), Jan (1), Lou (1)

#### Character Replies
1. **Fora** (immediate, 5000ms delay): "Oh, I totally get that. When everyone's in a different life stage, it can feel like you're talking a..."
2. **Jan** (immediate, 2500ms delay): "True, but common ground doesn't always have to be about personal life. You can ask about work projec..."
3. **Lou** (immediate, 3000ms delay): "Exactly, Jan! Sometimes it's less about the topic and more about just showing you're friendly. I jus..."

### prompt_5 ✅
- **Prompt**: "give me some starter packs for small talk that isn't cringe"
- **Duration**: 6.64s
- **Timestamp**: 2025-07-06T15:20:02.377Z
- **Conversation ID**: 206
- **Message Count**: 3
- **Theme**: Navigating breakroom dynamics
- **Skills**: conversation, communication, relationship building, workplace behavior, emotional intelligence

#### Character Reply Analysis
- **Total Replies**: 3
- **Immediate Replies**: 3
- **Delayed Replies**: 0
- **Average Delay**: 4000ms
- **Character Breakdown**: Jan (1), Lou (1), Fora (1)

#### Character Replies
1. **Jan** (immediate, 5000ms delay): "Okay, for non-cringe small talk, think low-stakes and open-ended. Try 'Any exciting plans for the lo..."
2. **Lou** (immediate, 3000ms delay): "Yeah, Jan's got the right idea! Also, try the 'observation + question' combo. Like, if someone's got..."
3. **Fora** (immediate, 4000ms delay): "Ugh, the observation method is solid, Lou. My go-to when I'm running on fumes is just commenting on ..."

### prompt_6 ✅
- **Prompt**: "i can't talk about the weather again"
- **Duration**: 3.27s
- **Timestamp**: 2025-07-06T15:20:11.020Z
- **Conversation ID**: 206
- **Message Count**: 3
- **Theme**: Navigating breakroom dynamics
- **Skills**: conversation, communication, relationship building, workplace behavior, emotional intelligence

#### Character Reply Analysis
- **Total Replies**: 3
- **Immediate Replies**: 3
- **Delayed Replies**: 0
- **Average Delay**: 3833ms
- **Character Breakdown**: Fora (1), Jan (1), Lou (1)

#### Character Replies
1. **Fora** (immediate, 3000ms delay): "Tell me about it. Weather chat gets old fast. 🫠 How about asking about their favorite local coffee ..."
2. **Jan** (immediate, 4000ms delay): "Exactly. You can pivot to something like, 'Any good books or podcasts you've come across lately?' It..."
3. **Lou** (immediate, 4500ms delay): "Ooh, or totally random but chill: 'Anyone else try that new food truck on Elm Street?' It opens up t..."
